"""
概念和板块过滤器使用示例

演示如何在其他文件中使用 concept_sector_filter.py 模块
"""

from concept_sector_filter import (
    filter_meaningful_concepts_and_sectors,
    filter_concept_list,
    filter_concept_dict,
    is_meaningful_concept,
    get_meaningless_items
)


def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    # 示例1: 过滤概念列表
    concepts = ['人工智能', '昨日涨停', '芯片概念', '融资融券', '新能源汽车', '央视50']
    filtered_concepts = filter_meaningful_concepts_and_sectors(concepts)
    print(f"原始概念: {concepts}")
    print(f"过滤后: {filtered_concepts}")
    
    # 示例2: 过滤概念字典
    concept_stats = {
        '人工智能': 15,
        '昨日涨停': 8,
        '芯片概念': 12,
        '融资融券': 5,
        '医疗器械': 7
    }
    filtered_stats = filter_meaningful_concepts_and_sectors(concept_stats)
    print(f"\n原始统计: {concept_stats}")
    print(f"过滤后统计: {filtered_stats}")


def example_individual_functions():
    """使用单独功能函数的示例"""
    print("\n=== 单独功能函数示例 ===")
    
    # 检查单个概念是否有意义
    concepts_to_check = ['人工智能', '昨日涨停', '芯片概念', 'ST板块']
    for concept in concepts_to_check:
        is_meaningful = is_meaningful_concept(concept)
        print(f"'{concept}' 是否有意义: {is_meaningful}")
    
    # 使用专门的列表过滤函数
    test_list = ['软件开发', '沪股通', '半导体', '深股通', '医疗器械']
    filtered_list = filter_concept_list(test_list)
    print(f"\n使用 filter_concept_list:")
    print(f"原始: {test_list}")
    print(f"过滤后: {filtered_list}")
    
    # 使用专门的字典过滤函数
    test_dict = {'AI概念': 10, 'MSCI': 5, '新能源': 8, '基金重仓': 3}
    filtered_dict = filter_concept_dict(test_dict)
    print(f"\n使用 filter_concept_dict:")
    print(f"原始: {test_dict}")
    print(f"过滤后: {filtered_dict}")


def example_get_meaningless_items():
    """获取无意义概念集合的示例"""
    print("\n=== 获取无意义概念集合 ===")
    
    meaningless_set = get_meaningless_items()
    print(f"无意义概念总数: {len(meaningless_set)}")
    print("部分无意义概念:")
    for i, item in enumerate(sorted(meaningless_set)):
        if i < 10:  # 只显示前10个
            print(f"  - {item}")
        elif i == 10:
            print(f"  ... 还有 {len(meaningless_set) - 10} 个")
            break


def example_real_world_usage():
    """模拟真实使用场景"""
    print("\n=== 真实使用场景模拟 ===")
    
    # 模拟从数据库或API获取的股票概念数据
    stock_concepts = {
        '比亚迪': ['新能源汽车', '锂电池', '昨日涨停', '深股通', '汽车整车'],
        '宁德时代': ['锂电池', '新能源汽车', '融资融券', 'MSCI', '储能'],
        '中芯国际': ['芯片概念', '半导体', '科创50', '沪股通', '集成电路'],
        '药明康德': ['医疗器械', 'CRO', '创新药', '基金重仓', '生物医药']
    }
    
    print("原始股票概念数据:")
    for stock, concepts in stock_concepts.items():
        print(f"  {stock}: {concepts}")
    
    # 过滤每只股票的概念
    filtered_stock_concepts = {}
    for stock, concepts in stock_concepts.items():
        filtered_concepts = filter_meaningful_concepts_and_sectors(concepts)
        filtered_stock_concepts[stock] = filtered_concepts
    
    print("\n过滤后的股票概念数据:")
    for stock, concepts in filtered_stock_concepts.items():
        print(f"  {stock}: {concepts}")
    
    # 统计过滤效果
    total_original = sum(len(concepts) for concepts in stock_concepts.values())
    total_filtered = sum(len(concepts) for concepts in filtered_stock_concepts.values())
    print(f"\n过滤效果统计:")
    print(f"  原始概念总数: {total_original}")
    print(f"  过滤后概念总数: {total_filtered}")
    print(f"  过滤掉的概念数: {total_original - total_filtered}")
    print(f"  过滤比例: {(total_original - total_filtered) / total_original * 100:.1f}%")


if __name__ == "__main__":
    example_basic_usage()
    example_individual_functions()
    example_get_meaningless_items()
    example_real_world_usage()
